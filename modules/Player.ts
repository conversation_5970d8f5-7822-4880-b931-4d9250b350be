import CachedAudioDownloader from './CachedAudioDownloader.js';

type Language = 'en' | 'no' | 'et';
type PlayerEvent = 'ended' | 'isPlaying' | 'isStopped';

interface EventCallback {
    event: PlayerEvent;
    callback: () => void;
}

export default class Player {
    private downloader: CachedAudioDownloader;
    private callbacks: EventCallback[] = [];
    private playing: boolean = false;
    private audioContext: AudioContext | null = null;

    constructor(downloader: CachedAudioDownloader) {
        this.downloader = downloader;
    }

    async play(textToPlay: string, lang: Language): Promise<void> {
        this.stop();

        const audioContext = this.getAudioContext();

        const audioBuffer = await this.downloader.getAudioFromText(textToPlay, lang);

        const audioSource = audioContext.createBufferSource();
        audioSource.buffer = await audioContext.decodeAudioData(audioBuffer);

        audioSource.addEventListener(
            "ended",
            () => {
                this.playing = false;
                this.notify('ended');
            },
            false
        );

        audioSource.connect(audioContext.destination);
        audioSource.start();

        this.playing = true;
    }

    isSuspended(): boolean {
        return this.getAudioContext().state === 'suspended';
    }

    pause(): void {
        this.getAudioContext().suspend();
        this.playing = false;
    }

    resume(): void {
        if (!this.isSuspended()) {
            throw new Error('Cannot resume when not suspended');
        }

        this.getAudioContext().resume();
        this.playing = true;
    }

    isPlaying(): boolean {
        return this.playing;
    }

    stop(): void {
        if (this.audioContext) {
            this.getAudioContext().close();
        }

        this.audioContext = null;

        this.playing = false;
    }

    private notify(event: PlayerEvent): void {
        this.callbacks
            .filter(each => each.event === event)
            .forEach(each => each.callback());
    }

    addEventListener(event: PlayerEvent, callback: () => void): void {
        this.callbacks.push({event, callback});
    }

    private getAudioContext(): AudioContext {
        if (!this.audioContext) {
            this.audioContext = new AudioContext();
        }

        return this.audioContext;
    }
}
