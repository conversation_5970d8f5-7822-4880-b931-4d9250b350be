import TextSplitter from "./TextSplitter.js";
import Player from "./Player.js";
import SectionPlayer from "./SectionPlayer.js";

let player;

export async function init(apiKey) {
    player = new SectionPlayer(new Player(apiKey));
}

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.command !== 'playOrStop') {
        return;
    }

    if (player.player.isPlaying()) {
        player.stop();
    } else {
        const text = window.getSelection().toString();

        if (!text.trim()) {
            return;
        }

        const lang = document.documentElement.lang || 'en';
        const paragraphs = new TextSplitter().toParagraphs(text);

        player.setLang(lang);
        player.setParagraphs(paragraphs);

        player.next();
        player.play();
    }
});
