import RollingMap from "./RollingMap.js";

export default class CachedAudioDownloader {

    rollingMap = new RollingMap(20);

    constructor(downloader) {
        this.downloader = downloader;
    }

    async getAudioFromText(text, lang) {
        const mapKey = lang + text;

        if (this.rollingMap.has(mapKey)) {
            return this.rollingMap.get(mapKey).arrayBuffer();
        }

        const audio = await this.downloader.getAudioFromText(text, lang);

        this.rollingMap.put(mapKey, audio);

        return audio.arrayBuffer();
    }
}
