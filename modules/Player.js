export default class Player {

    constructor(downloader) {
        this.downloader = downloader;
    }

    callbacks = [];

    playing = false;

    async play(textToPlay, lang) {
        this.stop();

        const audioContext = this.getAudioContext();

        const audioBuffer = await this.downloader.getAudioFromText(textToPlay, lang);

        const audioSource = audioContext.createBufferSource();
        audioSource.buffer = await audioContext.decodeAudioData(audioBuffer);

        audioSource.addEventListener(
            "ended",
            () => {
                this.playing = false;
                this.notify('ended');
            },
            false
        );

        audioSource.connect(audioContext.destination);
        audioSource.start();

        this.playing = true;
    }

    isSuspended() {
        return this.getAudioContext().state === 'suspended';
    }

    pause() {
        this.getAudioContext().suspend();
        this.playing = false;
    }

    resume() {
        if (!this.isSuspended()) {
            throw new Error('Cannot resume when not suspended');
        }

        this.getAudioContext().resume();
        this.playing = true;
    }

    isPlaying() {
        return this.playing;
    }

    stop() {
        if (this.audioContext) {
            this.getAudioContext().close();
        }

        this.audioContext = null;

        this.playing = false;
    }

    notify(event) {
        this.callbacks
            .filter(each => each.event === event)
            .forEach(each => each.callback());
    }

    addEventListener(event, callback) {
        this.callbacks.push({event, callback});
    }

    getAudioContext() {
        if (!this.audioContext) {
            this.audioContext = new AudioContext();
        }

        return this.audioContext;
    }
}
