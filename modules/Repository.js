const STORAGE_KEY = 't2s_key';
const VALID_SLOT_IDS = ['default', '1', '2', '3'];
const VALID_LANGS = ['en', 'et', 'no'];
const DEFAULT_SLOT_ID = VALID_SLOT_IDS[0];
const DEFAULT_LANG = VALID_LANGS[0];

export default class Repository {

    storage;
    paragraphs = [];

    constructor(storage) {
        this.storage = storage;
    }

    selectSlot(slotId) {
        slotId = VALID_SLOT_IDS.find(id => id === slotId) || DEFAULT_SLOT_ID;
        const state = this.fetchState()
        state.currentSlot = slotId;
        this.saveState(state);
    }

    getCurrentSlot() {
        return this.fetchState().currentSlot;
    }

    saveState(state) {
        this.storage.setItem(STORAGE_KEY, JSON.stringify(state));
    }

    fetchState() {
        const json = this.storage.getItem(STORAGE_KEY);

        const state = json ? JSON.parse(json) : {};

        const currentSlot = state.currentSlot || DEFAULT_SLOT_ID;
        const entries = state.entries || [];

        return new State(currentSlot, entries);
    }

    getParagraphs() {
        return this.paragraphs;
    }

    getLang() {
        const state = this.fetchState();

        return this.fetchState()
            .findOrCreateEntry(state.currentSlot).lang || DEFAULT_LANG;
    }

    selectLang(lang) {
        lang = VALID_LANGS.find(l => l === lang) || DEFAULT_LANG;
        const state = this.fetchState();
        state.findOrCreateEntry(state.currentSlot).lang = lang;
        this.saveState(state);
    }

    setParagraphs(paragraphs) {
        this.paragraphs = paragraphs;
    }

    deleteSentenceId(slotId) {
        const state = this.fetchState();
        state.findOrCreateEntry(slotId).sentenceId = undefined;
        this.saveState(state);

    }

    saveSentenceId(sentenceId) {
        const state = this.fetchState();
        state.findOrCreateEntry(state.currentSlot).sentenceId = sentenceId;
        this.saveState(state);
    }

    getSentenceId() {
        const state = this.fetchState();
        return state.findOrCreateEntry(state.currentSlot).sentenceId;
    }
}

class Entry {
    constructor(slotId, sentenceId, lang) {
        this.slotId = slotId;
        this.sentenceId = sentenceId;
        this.lang = lang;
    }
}

class State {
    constructor(currentSlot, entries) {
        this.currentSlot = currentSlot;
        this.entries = entries;
    }

    findOrCreateEntry(slotId) {
        let entry = this.entries.find(entry => entry.slotId === slotId);

        if (!entry) {
            entry = new Entry(slotId, null, null);
            this.entries.push(entry);
        }

        return entry;
    }
}
