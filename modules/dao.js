import Sentence from "./Sentence.js";
import Paragraph from "./Paragraph.js";

export async function storeParagraphs(paragraphs, slotId) {
    const params = new URLSearchParams();
    if (slotId !== null) {
        params.append('slotId', slotId);
    }
    params.append('cmd', 'store-json');

    const url = `api.php?` + params.toString();

    return fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(paragraphs)
    }).catch(error => console.error(error));
}

export async function fetchParagraphs(slotId) {
    const params = new URLSearchParams();
    if (slotId !== null) {
        params.append('slotId', slotId);
    }

    params.append('cmd', 'fetch-json');

    const url = `api.php?` + params.toString();

    const json = await fetch(url).then(r => r.json());

    return addPrototypes(json);
}

function addPrototypes(rawObjects) {
    return rawObjects.map(each => {
        const p = new Paragraph([]);
        p.id = each.id;
        p.sentences = each.sentences.map(each => {
            const s = new Sentence('', '');
            s.id = each.id;
            s.text = each.text;
            return s;
        });

        return p;
    });
}