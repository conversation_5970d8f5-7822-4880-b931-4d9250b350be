import cleanUpText from "./textCleanerApi.js";

const langRadios = document.getElementsByName('lang');
const slotLoadLinks = document.querySelectorAll('#slots a[data-slot-type="load"]');
const slotSaveLinks = document.querySelectorAll('#slots a[data-slot-type="save"]');

const clipboardButton = document.getElementById('clipboard-button');
const clipboardCleanedButton = document.getElementById('clipboard-cleaned-button');
const playPauseButton = document.getElementById('play-pause-button');

export default class Gui {

    static PRESS_PLAY_PAUSE = 'PRESS_PLAY_PAUSE';
    static PRESS_NEXT = 'PRESS_NEXT';
    static PRESS_BACK = 'PRESS_BACK';
    static LANG_SELECT = 'LANG_SELECT';
    static SLOT_SAVE = 'SLOT_SAVE';
    static SLOT_LOAD = 'SLOT_LOAD';
    static SELECT_SENTENCE = 'SELECT_SENTENCE';
    static PASTE_FROM_CLIPBOARD = 'PASTE_FROM_CLIPBOARD';
    static PASTE_FROM_CLIPBOARD_CLEANED = 'PASTE_FROM_CLIPBOARD_CLEANED';
    static SHOW_MESSAGE = 'SHOW_MESSAGE';

    repository;

    constructor(repository) {
        this.repository = repository;
        this.listeners = [];

        clipboardButton.addEventListener('click',
            () => this.pasteFromClipboard());

        clipboardCleanedButton.addEventListener('click',
            () => this.pasteFromClipboardCleaned());

        playPauseButton.addEventListener('click',
            () => this.notify(Gui.PRESS_PLAY_PAUSE));

        document.getElementById('back-button')
            .addEventListener('click', () => this.notify(Gui.PRESS_BACK));

        document.getElementById('next-button')
            .addEventListener('click', () => this.notify(Gui.PRESS_NEXT));

        this.initLangRadios();
        this.initSlotSaveLinks();
        this.initSlotLoadLinks();

        this.refresh();
    }

    addEventListener(eventName, callback) {
        this.listeners.push({eventName, callback});
    }

    notify(eventName, data) {
        this.listeners.forEach(each => {
            if (each.eventName === eventName) {
                each.callback(data);
            }
        });
    }

    setIsPlaying(isPlaying) {
        playPauseButton.innerHTML = isPlaying ? 'II' : '&gt;';
    }

    refresh() {
        this.refreshLangRadios();

        this.createTable(this.repository.getParagraphs());

        this.refreshSlotLinks(this.repository.getCurrentSlot());
    }

    initSlotLoadLinks() {
        for (const link of slotLoadLinks) {
            link.addEventListener('click', async e => {
                e.preventDefault();

                const slotId = link.getAttribute('data-slot-id') || null;

                this.notify(Gui.SLOT_LOAD, slotId);
            });
        }
    }

    initSlotSaveLinks() {
        for (const link of slotSaveLinks) {
            link.addEventListener('click', async e => {
                e.preventDefault();

                const slotId = link.getAttribute('data-slot-id') || null;

                this.notify(Gui.SLOT_SAVE, slotId);
            });
        }
    }

    refreshLangRadios() {
        const lang = this.repository.getLang();
        for (const langRadio of langRadios) {
            langRadio.checked = langRadio.value === lang;
        }
    }

    initLangRadios() {
        for (const langRadio of langRadios) {
            langRadio.addEventListener('change', () => {
                this.notify(Gui.LANG_SELECT, langRadio.value);
            });
        }
    }

    createTable(paragraphs) {
        const container = document.getElementById('result-table');

        container.replaceChildren();

        for (const paragraph of paragraphs) {
            const row = document.createElement('div');

            row.setAttribute('id', paragraph.id);

            const col2 = document.createElement('div');

            paragraph.sentences.forEach(sentence => {
                const span = this.createSpan(sentence.toString() + ' ', sentence.id);

                span.ondblclick = () => this.notify(Gui.SELECT_SENTENCE, sentence.id);

                col2.appendChild(span);
            });

            col2.appendChild(document.createElement('br'));
            col2.appendChild(document.createElement('br'));

            row.appendChild(col2);

            container.appendChild(row);
        }
    }

    refreshSlotLinks(currentSlot) {
        for (const link of slotLoadLinks) {
            if (link.getAttribute('data-slot-id') === currentSlot) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        }
    }

    createSpan(text, id) {
        const span = document.createElement('span');

        span.setAttribute('id', id);
        span.innerText = text;

        return span;
    }

    highlightSentence(sentenceId) {
        const span = document.getElementById(sentenceId);

        if (span) {
            span.style.backgroundColor = '#FAE5D3';
        }
    }

    clearHighlight(sentenceId) {
        const span = document.getElementById(sentenceId);

        if (span) {
            span.style.backgroundColor = '';
        }
    }

    focusSentence(sentenceId) {
        if (sentenceId === undefined) {
            return;
        }

        const element = document.getElementById(sentenceId);
        const rect = element.getBoundingClientRect();
        const scroll = window.scrollY;

        if (rect.top > 300) {
            const desiredPosition = rect.top + scroll - 120;
            window.scrollTo({ top: desiredPosition, behavior: 'smooth' });
        }
    }

    pasteFromClipboard() {
        this.notify(Gui.SHOW_MESSAGE, 'hello');

        navigator.clipboard.readText()
            .then(text => this.notify(Gui.PASTE_FROM_CLIPBOARD, text));
    }

    pasteFromClipboardCleaned() {
        navigator.clipboard.readText()
            .then(text => cleanUpText(text))
            .then(sentences => this.notify(Gui.PASTE_FROM_CLIPBOARD_CLEANED, sentences));
    }
}