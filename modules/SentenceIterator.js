export default class SentenceIterator {

    constructor(paragraphs = []) {
        this.paragraphs = [];
        this.paragraphIndex = 0;
        this.sentenceIndex = 0;

        this.load(paragraphs);
    }

    load(paragraphs) {
        this.paragraphs = paragraphs;
        this.paragraphIndex = 0;
        this.sentenceIndex = 0;
        this.paragraph = undefined;
        this.sentence = undefined;
    }

    getAllIds() {
        const ids = [];

        const clone = this.clone().reset();

        while (clone.hasNext()) {
            clone.nextSentence();
            ids.push(clone.currentSentence().id);
        }

        return ids;
    }

    clone() {
        const clone = new SentenceIterator(this.paragraphs);
        clone.paragraphIndex = this.paragraphIndex;
        clone.sentenceIndex = this.sentenceIndex;
        clone.paragraph = this.paragraph;
        clone.sentence = this.sentence;
        return clone;
    }

    containsSentenceId(sentenceId) {
        return this.getAllIds().includes(sentenceId);
    }

    reset() {
        this.paragraphIndex = 0;
        this.sentenceIndex = 0;
        this.sentence = undefined;
        return this;
    }

    currentSentence() {
        return this.sentence;
    }

    currentParagraph() {
        return this.paragraph;
    }

    hasCurrent() {
        return this.sentence !== undefined;
    }

    previousSentenceId() {
        if (!this.hasCurrent()) {
            return undefined;
        }

        const currentId = this.currentSentence().id;

        const ids = [];
        for (const id of this.getAllIds()) {
            if (id === currentId) {
                return ids.pop();
            }
            ids.push(id);
        }
    }

    nextSentence() {
        this.paragraph = this.paragraphs[this.paragraphIndex];
        this.sentence = this.paragraph.sentences[this.sentenceIndex];

        this.sentenceIndex++;

        if (this.sentenceIndex >= this.paragraph.sentences.length) {
            this.paragraphIndex++;
            this.sentenceIndex = 0;
        }

        return this.sentence;
    }

    nextSentenceId() {
        return this.hasNext()
            ? this.clone().nextSentence().id : undefined;
    }

    iterateToId(sentenceId) {
        this.reset();

        while (this.hasNext()) {

            this.nextSentence();

            if (this.currentSentence().id === sentenceId) {
                return;
            }
        }
    }

    hasNext() {
        return this.paragraphIndex < this.paragraphs.length;
    }

    hasPrevious() {
        return this.previousSentenceId() !== undefined;
    }

    isEmpty() {
        return this.paragraphs.length === 0
            || this.paragraphs.every(p => p.isEmpty());
    }
}
