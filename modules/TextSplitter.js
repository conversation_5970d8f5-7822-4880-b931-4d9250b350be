import Paragraph from "./Paragraph.js";
import Sentence from "./Sentence.js";

export default class TextSplitter {

    toParagraphs(text) {
        const paragraphs = text.split(/(?:\r?\n\s*){2,}/);

        return paragraphs.map((p, index) => {
            const idPrefix = `p_${index + 1}`;

            return new Paragraph(this.toSentences(p, idPrefix));
        });
    }

    toSentences(text, idPrefix) {
        return text.split(/(?<=[.?!])\s+/)
            .map((sentence, index) => new Sentence(sentence, `${idPrefix}_s_${index + 1}`));
    }

}