import RollingMap from "./RollingMap.js";
import AudioDownloader from "./AudioDownloader.js";

type Language = 'en' | 'no' | 'et';

export default class CachedAudioDownloader {
    private rollingMap = new RollingMap<Blob>(20);
    private downloader: AudioDownloader;

    constructor(downloader: AudioDownloader) {
        this.downloader = downloader;
    }

    async getAudioFromText(text: string, lang: Language): Promise<ArrayBuffer> {
        const mapKey = lang + text;

        if (this.rollingMap.has(mapKey)) {
            const cachedBlob = this.rollingMap.get(mapKey);
            if (cachedBlob) {
                return cachedBlob.arrayBuffer();
            }
        }

        const audio = await this.downloader.getAudioFromText(text, lang);

        // Convert to Blob if it's an ArrayBuffer
        const audioBlob = audio instanceof Blob ? audio : new Blob([audio]);

        this.rollingMap.put(mapKey, audioBlob);

        return audioBlob.arrayBuffer();
    }
}
