import TextSplitter from "./TextSplitter.js";

export async function start(baseUrl) {
    const text = window.getSelection().toString();

    const lang = document.documentElement.lang;

    return storeText(baseUrl, toParagraphs(text)).then(() => {
        window.open(`${baseUrl}/?slotId=default&lang=${lang}`, '_blank');
    });
}

function storeText(baseUrl, paragraphs) {
    const url =  `${baseUrl}/api.php?cmd=store-json`;

    return fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(paragraphs)
    }).catch(error => console.error(error));
}

export function toParagraphs(searchText) {
    const result = [];

    let runner = document.evaluate(`//*[contains(text(), '${searchText}')]`,
        document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;

    while (runner) {
        if (runner.innerText.trim()) {
            const paragraphs = new TextSplitter().toParagraphs(runner.innerText);

            result.push(...paragraphs);
        }

        runner = runner.nextElementSibling;
    }

    return result;
}
