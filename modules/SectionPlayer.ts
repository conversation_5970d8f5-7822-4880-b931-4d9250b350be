import SentenceIterator from "./SentenceIterator.ts";
import Player from "./Player.ts";
import Paragraph from "./Paragraph.ts";

type Language = 'en' | 'no' | 'et';
type SectionPlayerEvent = 'selected' | 'isPlaying' | 'isStopped';

interface EventListener {
    eventName: SectionPlayerEvent;
    callback: (data?: any) => void;
}

export default class SectionPlayer {
    private sentenceIterator = new SentenceIterator();
    private listeners: EventListener[] = [];
    private lang: Language | undefined;
    private player: Player;

    constructor(player: Player) {
        this.player = player;

        this.player.addEventListener('ended', () => {
            if (this.sentenceIterator.hasNext()) {
                this.nextSentence();
                this.play();

                this.notify('selected', this.currentSentenceId());

            } else {
                this.stop();
            }
        });
    }

    setLang(lang: Language): void {
        this.lang = lang;
    }

    addEventListener(eventName: SectionPlayerEvent, callback: (data?: any) => void): void {
        this.listeners.push({eventName, callback});
    }

    private notify(eventName: SectionPlayerEvent, data?: any): void {
        this.listeners.forEach(each => {
            if (each.eventName === eventName) {
                each.callback(data);
            }
        });
    }

    setParagraphs(paragraphs: Paragraph[]): void {
        this.sentenceIterator.load(paragraphs);
    }

    goToSentence(sentenceId: string | undefined): void {
        if (sentenceId) {
            this.doStoringState(() => this.selectSentence(sentenceId));
        }
    }

    private selectSentence(sentenceId: string): void {
        if (this.sentenceIterator.isEmpty()) {
            return;
        }

        if (this.sentenceIterator.containsSentenceId(sentenceId)) {
            this.sentenceIterator.iterateToId(sentenceId);
        } else {
            this.sentenceIterator.reset();
            this.sentenceIterator.nextSentence();
        }

        this.notify('selected', this.currentSentenceId());
    }

    currentSentenceId(): string | undefined {
        return this.sentenceIterator.currentSentence()?.id;
    }

    nextSentence(): void {
        if (!this.sentenceIterator.hasNext()) {
            return;
        }

        const nextId = this.sentenceIterator.nextSentenceId();

        this.goToSentence(nextId);
    }

    playPause(): void {
        if (this.player.isSuspended()) {
            this.player.resume();

            this.notify('isPlaying');

        } else if (this.player.isPlaying()) {
            this.pause();
        } else {
            this.play();

            this.notify('isPlaying');
        }
    }

    play(): void {
        if (!this.sentenceIterator.hasCurrent() || !this.lang) {
            return;
        }

        const currentSentence = this.sentenceIterator.currentSentence();
        if (currentSentence) {
            this.player.play(currentSentence.toString(), this.lang);
            this.notify('isPlaying');
        }
    }

    pause(): void {
        this.player.pause();

        this.notify('isStopped');
    }

    stop(): void {
        this.player.stop();

        this.notify('isStopped');
    }

    next(): void {
        if (!this.sentenceIterator.hasNext()) {
            return;
        }

        const id = this.sentenceIterator.nextSentence().id;

        this.doStoringState(() => this.goToSentence(id));
    }

    private doStoringState(action: () => void): void {
        const wasPlaying = this.player.isPlaying();

        if (wasPlaying) {
            this.stop();
        }

        action();

        if (wasPlaying) {
            this.play();
        }
    }

    back(): void {
        if (!this.sentenceIterator.hasPrevious()) {
            return;
        }

        const id = this.sentenceIterator.previousSentenceId();

        this.doStoringState(() => this.goToSentence(id));
    }

    getAllIds(): string[] {
        return this.sentenceIterator.getAllIds();
    }

}
