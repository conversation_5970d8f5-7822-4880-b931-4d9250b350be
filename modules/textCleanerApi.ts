const prompt = `Your role is to clean up text copied from pdf-s.

The output text will be used for text-to-speech.
Final result should be meaningful sentences but the original text may
contain computer code, parts of equations and other such things that are
not meaningful to translate to speech.

In case there are before mentioned things then these should be removed.
If some text is removed replace it with phrase "- text removed -"
The text should be split into sentences separated by "|||" (three pipe characters).

Example output: <sentence 1> ||| <sentence 2> ||| ...`;

interface OpenAIMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

interface OpenAIRequestBody {
    model: string;
    messages: OpenAIMessage[];
    max_tokens: number;
    temperature: number;
}

interface OpenAIChoice {
    message: {
        content: string;
    };
    finish_reason: string;
}

interface OpenAIResponse {
    choices: OpenAIChoice[];
}

async function sendRequest(text: string, apiKey: string): Promise<Response> {
    const url = 'https://api.openai.com/v1/chat/completions';

    const body: OpenAIRequestBody = {
        model: 'gpt-4o-mini-2024-07-18',
        messages: [
            {"role": "system", "content": prompt},
            {"role": "user", "content": text}
        ],
        max_tokens: 4000,
        temperature: 0
    };

    let response: Response;

    try {
        response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify(body)
        });

    } catch (error) {
        throw new Error('Error calling OpenAI API:' + error);
    }

    if (response.ok) {
        return response;
    } else {
        throw new Error(`Error: ${response.status} - ${response.statusText}`);
    }
}

async function callOpenAI(text: string, apiKey: string): Promise<string[]> {

    const response = await sendRequest(text, apiKey);

    try {
        const json: OpenAIResponse = await response.json();
        console.log('Finish reason:', json.choices[0].finish_reason);

        return splitToSentences(json.choices[0].message.content);

    } catch (error) {
        console.error('Error reading response', error);
        throw error;
    }
}

function splitToSentences(text: string): string[] {
    const sentences = text.split('|||');
    return sentences
        .map(sentence => sentence.trimEnd())
        .map(sentence => sentence.trimStart())
        .map(sentence => sentence.replace(/"/g, ''));
}

async function fetchApiKey(): Promise<string> {
    const response = await fetch('settings.json');
    const settings = await response.json();
    return settings['api-key-openai'];
}

export default async function cleanUpText(text: string): Promise<string[]> {
    return callOpenAI(text, await fetchApiKey());
}


