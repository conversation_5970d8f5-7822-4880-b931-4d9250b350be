export default class RollingMap {

    constructor(capacity) {
        this.capacity = capacity;
        this.map = new Map();
        this.keys = [];
    }

    put(key, value) {
        if (this.map.has(key)) {
            this.map.set(key, value);
        } else {
            if (this.keys.length === this.capacity) {
                const oldestKey = this.keys.shift();
                this.map.delete(oldestKey);
            }
            this.map.set(key, value);
            this.keys.push(key);
        }
    }

    get(key) {
        return this.map.get(key);
    }

    has(key) {
        return this.map.has(key);
    }
}
