import Paragraph from "./Paragraph.ts";
import Sentence from "./Sentence.ts";

export default class TextSplitter {

    toParagraphs(text: string): Paragraph[] {
        const paragraphs = text.split(/(?:\r?\n\s*){2,}/);

        return paragraphs.map((p, index) => {
            const idPrefix = `p_${index + 1}`;

            return new Paragraph(this.toSentences(p, idPrefix));
        });
    }

    toSentences(text: string, idPrefix: string): Sentence[] {
        return text.split(/(?<=[.?!])\s+/)
            .map((sentence, index) => new Sentence(sentence, `${idPrefix}_s_${index + 1}`));
    }

}