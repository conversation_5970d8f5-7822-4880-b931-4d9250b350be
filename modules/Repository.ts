import Paragraph from './Paragraph.js';

const STORAGE_KEY = 't2s_key';
const VALID_SLOT_IDS = ['default', '1', '2', '3'] as const;
const VALID_LANGS = ['en', 'et', 'no'] as const;
const DEFAULT_SLOT_ID = VALID_SLOT_IDS[0];
const DEFAULT_LANG = VALID_LANGS[0];

type SlotId = typeof VALID_SLOT_IDS[number];
type Language = typeof VALID_LANGS[number];

interface Storage {
    getItem(key: string): string | null;
    setItem(key: string, value: string): void;
}

export default class Repository {
    private storage: Storage;
    paragraphs: Paragraph[] = [];

    constructor(storage: Storage) {
        this.storage = storage;
    }

    selectSlot(slotId: string): void {
        const validSlotId = VALID_SLOT_IDS.find(id => id === slotId) || DEFAULT_SLOT_ID;
        const state = this.fetchState()
        state.currentSlot = validSlotId;
        this.saveState(state);
    }

    getCurrentSlot(): SlotId {
        return this.fetchState().currentSlot;
    }

    private saveState(state: State): void {
        this.storage.setItem(STORAGE_KEY, JSON.stringify(state));
    }

    private fetchState(): State {
        const json = this.storage.getItem(STORAGE_KEY);

        const state = json ? JSON.parse(json) : {};

        const currentSlot = state.currentSlot || DEFAULT_SLOT_ID;
        const entries = state.entries || [];

        return new State(currentSlot, entries);
    }

    getParagraphs(): Paragraph[] {
        return this.paragraphs;
    }

    getLang(): Language {
        const state = this.fetchState();

        return this.fetchState()
            .findOrCreateEntry(state.currentSlot).lang || DEFAULT_LANG;
    }

    selectLang(lang: string): void {
        const validLang = VALID_LANGS.find(l => l === lang) || DEFAULT_LANG;
        const state = this.fetchState();
        state.findOrCreateEntry(state.currentSlot).lang = validLang;
        this.saveState(state);
    }

    setParagraphs(paragraphs: Paragraph[]): void {
        this.paragraphs = paragraphs;
    }

    deleteSentenceId(slotId: string): void {
        const state = this.fetchState();
        state.findOrCreateEntry(slotId).sentenceId = undefined;
        this.saveState(state);
    }

    saveSentenceId(sentenceId: string): void {
        const state = this.fetchState();
        state.findOrCreateEntry(state.currentSlot).sentenceId = sentenceId;
        this.saveState(state);
    }

    getSentenceId(): string | undefined {
        const state = this.fetchState();
        return state.findOrCreateEntry(state.currentSlot).sentenceId;
    }
}

class Entry {
    slotId: string;
    sentenceId: string | undefined;
    lang: Language | undefined;

    constructor(slotId: string, sentenceId: string | null, lang: Language | null) {
        this.slotId = slotId;
        this.sentenceId = sentenceId || undefined;
        this.lang = lang || undefined;
    }
}

class State {
    currentSlot: SlotId;
    entries: Entry[];

    constructor(currentSlot: SlotId, entries: Entry[]) {
        this.currentSlot = currentSlot;
        this.entries = entries;
    }

    findOrCreateEntry(slotId: string): Entry {
        let entry = this.entries.find(entry => entry.slotId === slotId);

        if (!entry) {
            entry = new Entry(slotId, null, null);
            this.entries.push(entry);
        }

        return entry;
    }
}
