import Paragraph from './Paragraph.js';

const STATE_FILE = 'state.json';
const VALID_SLOT_IDS = ['default', '1', '2', '3'] as const;
const VALID_LANGS = ['en', 'et', 'no'] as const;
const DEFAULT_SLOT_ID = VALID_SLOT_IDS[0];
const DEFAULT_LANG = VALID_LANGS[0];

type SlotId = typeof VALID_SLOT_IDS[number];
type Language = typeof VALID_LANGS[number];

interface ApiStorage {
    getState(): Promise<string | null>;
    setState(data: string): Promise<void>;
}

class FileApiStorage implements ApiStorage {
    async getState(): Promise<string | null> {
        try {
            const response = await fetch(`api.php?cmd=fetch-state&file=${STATE_FILE}`);
            if (response.ok) {
                return await response.text();
            }
            return null;
        } catch (error) {
            console.error('Failed to fetch state:', error);
            return null;
        }
    }

    async setState(data: string): Promise<void> {
        try {
            await fetch(`api.php?cmd=store-state&file=${STATE_FILE}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: data
            });
        } catch (error) {
            console.error('Failed to store state:', error);
            throw error;
        }
    }
}

export default class Repository {
    private storage: ApiStorage;
    paragraphs: Paragraph[] = [];
    private stateCache: State | null = null;

    constructor(storage?: ApiStorage) {
        this.storage = storage || new FileApiStorage();
    }

    async selectSlot(slotId: string): Promise<void> {
        const validSlotId = VALID_SLOT_IDS.find(id => id === slotId) || DEFAULT_SLOT_ID;
        const state = await this.fetchState();
        state.currentSlot = validSlotId;
        await this.saveState(state);
    }

    async getCurrentSlot(): Promise<SlotId> {
        const state = await this.fetchState();
        return state.currentSlot;
    }

    private async saveState(state: State): Promise<void> {
        this.stateCache = state;
        await this.storage.setState(JSON.stringify(state));
    }

    private async fetchState(): Promise<State> {
        // Use cache if available to avoid unnecessary API calls
        if (this.stateCache) {
            return this.stateCache;
        }

        const json = await this.storage.getState();
        const state = json ? JSON.parse(json) : {};

        const currentSlot = state.currentSlot || DEFAULT_SLOT_ID;
        const entries = state.entries || [];

        const stateObj = new State(currentSlot, entries);
        this.stateCache = stateObj;
        return stateObj;
    }

    getParagraphs(): Paragraph[] {
        return this.paragraphs;
    }

    async getLang(): Promise<Language> {
        const state = await this.fetchState();
        return state.findOrCreateEntry(state.currentSlot).lang || DEFAULT_LANG;
    }

    async selectLang(lang: string): Promise<void> {
        const validLang = VALID_LANGS.find(l => l === lang) || DEFAULT_LANG;
        const state = await this.fetchState();
        state.findOrCreateEntry(state.currentSlot).lang = validLang;
        await this.saveState(state);
    }

    setParagraphs(paragraphs: Paragraph[]): void {
        this.paragraphs = paragraphs;
    }

    async deleteSentenceId(slotId: string): Promise<void> {
        const state = await this.fetchState();
        state.findOrCreateEntry(slotId).sentenceId = undefined;
        await this.saveState(state);
    }

    async saveSentenceId(sentenceId: string): Promise<void> {
        const state = await this.fetchState();
        state.findOrCreateEntry(state.currentSlot).sentenceId = sentenceId;
        await this.saveState(state);
    }

    async getSentenceId(): Promise<string | undefined> {
        const state = await this.fetchState();
        return state.findOrCreateEntry(state.currentSlot).sentenceId;
    }

    // Method to clear cache when needed
    clearCache(): void {
        this.stateCache = null;
    }
}

class Entry {
    slotId: string;
    sentenceId: string | undefined;
    lang: Language | undefined;

    constructor(slotId: string, sentenceId: string | null, lang: Language | null) {
        this.slotId = slotId;
        this.sentenceId = sentenceId || undefined;
        this.lang = lang || undefined;
    }
}

class State {
    currentSlot: SlotId;
    entries: Entry[];

    constructor(currentSlot: SlotId, entries: Entry[]) {
        this.currentSlot = currentSlot;
        this.entries = entries;
    }

    findOrCreateEntry(slotId: string): Entry {
        let entry = this.entries.find(entry => entry.slotId === slotId);

        if (!entry) {
            entry = new Entry(slotId, null, null);
            this.entries.push(entry);
        }

        return entry;
    }
}
