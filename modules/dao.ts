import Sentence from "./Sentence.ts";
import Paragraph from "./Paragraph.ts";

export async function storeParagraphs(paragraphs: Paragraph[], slotId: string): Promise<Response | void> {
    const params = new URLSearchParams();
    if (slotId !== null) {
        params.append('slotId', slotId);
    }
    params.append('cmd', 'store-json');

    const url = `api.php?` + params.toString();

    return fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(paragraphs)
    }).catch(error => console.error(error));
}

export async function fetchParagraphs(slotId: string): Promise<Paragraph[]> {
    const params = new URLSearchParams();
    if (slotId !== null) {
        params.append('slotId', slotId);
    }

    params.append('cmd', 'fetch-json');

    const url = `api.php?` + params.toString();

    const json = await fetch(url).then(r => r.json());

    return addPrototypes(json);
}

interface RawSentence {
    id: string;
    text: string;
}

interface RawParagraph {
    id?: string;
    sentences: RawSentence[];
}

function addPrototypes(rawObjects: RawParagraph[]): Paragraph[] {
    return rawObjects.map(each => {
        const p = new Paragraph([]);
        p.id = each.id;
        p.sentences = each.sentences.map(each => {
            const s = new Sentence('', '');
            s.id = each.id;
            s.text = each.text;
            return s;
        });

        return p;
    });
}