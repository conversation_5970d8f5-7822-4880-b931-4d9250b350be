type Language = 'en' | 'no' | 'et';

export default class AudioDownloader {
    private apiKey: string;

    constructor(apiKey: string) {
        this.apiKey = apiKey;
    }

    async getAudioFromText(text: string, lang: Language): Promise<Blob | ArrayBuffer> {
        return lang === 'et'
            ? this.getAudioFromTextEst(text)
            : this.getAudioFromTextGoogle(text, lang)
    }

    async getAudioFromTextEst(text: string): Promise<Blob> {
        const url = 'https://api.tartunlp.ai/text-to-speech/v2'

        return fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: text,
                    "speaker": "mari",
                    "speed": 1
                })
            })
            .then(response => {
                return response.blob();
            })
            .catch(error => {
                console.error(error);
                throw error;
            });
    }

    async getAudioFromTextGoogle(text: string, lang: Language): Promise<Blob> {

        const langCode = lang === 'en' ? 'en-US' : 'nb-NO';
        const langName = lang === 'en' ? 'en-US-Wavenet-I' : 'nb-NO-Wavenet-E';
        const speakingRate = lang === 'no' ? 0.7 : 1;

        const url = `https://texttospeech.googleapis.com/v1/text:synthesize?key=${this.apiKey}`;

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                input: {
                    text
                },
                voice: {
                    languageCode: langCode,
                    name: langName
                },
                audioConfig: {
                    audioEncoding: 'LINEAR16',
                    pitch: 0,
                    speakingRate: speakingRate
                }
            })
        });

        const data = await response.json();

        return this.toBlob(data.audioContent);
    }

    private toBlob(audioBase64: string): Blob {
        const binaryString = atob(audioBase64);
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        return new Blob([bytes], { type: 'audio/mp3' });
    }
}
