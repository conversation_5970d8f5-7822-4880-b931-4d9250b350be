import SentenceIterator from "./SentenceIterator.js";

export default class SectionPlayer {

    sentenceIterator = new SentenceIterator();
    listeners = [];
    lang;

    constructor(player) {
        this.player = player;

        this.player.addEventListener('ended', () => {
            if (this.sentenceIterator.hasNext()) {
                this.nextSentence();
                this.play();

                this.notify('selected', this.currentSentenceId());

            } else {
                this.stop();
            }
        });
    }

    setLang(lang) {
        this.lang = lang;
    }

    addEventListener(eventName, callback) {
        this.listeners.push({eventName, callback});
    }

    notify(eventName, data) {
        this.listeners.forEach(each => {
            if (each.eventName === eventName) {
                each.callback(data);
            }
        });
    }

    setParagraphs(paragraphs) {
        this.sentenceIterator.load(paragraphs);
    }

    goToSentence(sentenceId) {
        this.doStoringState(() => this.selectSentence(sentenceId));

    }

    selectSentence(sentenceId) {
        if (this.sentenceIterator.isEmpty()) {
            return;
        }

        if (this.sentenceIterator.containsSentenceId(sentenceId)) {
            this.sentenceIterator.iterateToId(sentenceId);
        } else {
            this.sentenceIterator.reset();
            this.sentenceIterator.nextSentence();
        }

        this.notify('selected', this.currentSentenceId());
    }

    currentSentenceId() {
        return this.sentenceIterator.currentSentence()?.id;
    }

    nextSentence() {
        if (!this.sentenceIterator.hasNext()) {
            return;
        }

        const nextId = this.sentenceIterator.nextSentenceId();

        this.goToSentence(nextId);
    }

    playPause() {
        if (this.player.isSuspended()) {
            this.player.resume();

            this.notify('isPlaying');

        } else if (this.player.isPlaying()) {
            this.pause();
        } else {
            this.play();

            this.notify('isPlaying');
        }
    }

    play() {
        if (!this.sentenceIterator.hasCurrent()) {
            return;
        }

        this.player.play(this.sentenceIterator.currentSentence().toString(), this.lang);

        this.notify('isPlaying');
    }

    pause() {
        this.player.pause();

        this.notify('isStopped');
    }

    stop() {
        this.player.stop();

        this.notify('isStopped');
    }

    next() {
        if (!this.sentenceIterator.hasNext()) {
            return;
        }

        const id = this.sentenceIterator.nextSentence().id;

        this.doStoringState(() => this.goToSentence(id));
    }

    doStoringState(action) {
        const wasPlaying = this.player.isPlaying();

        if (wasPlaying) {
            this.stop();
        }

        action();

        if (wasPlaying) {
            this.play();
        }
    }

    back() {
        if (!this.sentenceIterator.hasPrevious()) {
            return;
        }

        const id = this.sentenceIterator.previousSentenceId();

        this.doStoringState(() => this.goToSentence(id));
    }

    getAllIds() {
        return this.sentenceIterator.getAllIds();
    }

}
