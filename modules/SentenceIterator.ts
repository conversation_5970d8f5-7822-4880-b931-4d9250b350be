import Paragraph from './Paragraph.js';
import Sentence from './Sentence.js';

export default class SentenceIterator {
    private paragraphs: Paragraph[] = [];
    private paragraphIndex: number = 0;
    private sentenceIndex: number = 0;
    private paragraph: Paragraph | undefined;
    private sentence: Sentence | undefined;

    constructor(paragraphs: Paragraph[] = []) {
        this.load(paragraphs);
    }

    load(paragraphs: Paragraph[]): void {
        this.paragraphs = paragraphs;
        this.paragraphIndex = 0;
        this.sentenceIndex = 0;
        this.paragraph = undefined;
        this.sentence = undefined;
    }

    getAllIds(): string[] {
        const ids: string[] = [];

        const clone = this.clone().reset();

        while (clone.hasNext()) {
            clone.nextSentence();
            const sentence = clone.currentSentence();
            if (sentence) {
                ids.push(sentence.id);
            }
        }

        return ids;
    }

    clone(): SentenceIterator {
        const clone = new SentenceIterator(this.paragraphs);
        clone.paragraphIndex = this.paragraphIndex;
        clone.sentenceIndex = this.sentenceIndex;
        clone.paragraph = this.paragraph;
        clone.sentence = this.sentence;
        return clone;
    }

    containsSentenceId(sentenceId: string): boolean {
        return this.getAllIds().includes(sentenceId);
    }

    reset(): SentenceIterator {
        this.paragraphIndex = 0;
        this.sentenceIndex = 0;
        this.sentence = undefined;
        return this;
    }

    currentSentence(): Sentence | undefined {
        return this.sentence;
    }

    currentParagraph(): Paragraph | undefined {
        return this.paragraph;
    }

    hasCurrent(): boolean {
        return this.sentence !== undefined;
    }

    previousSentenceId(): string | undefined {
        if (!this.hasCurrent()) {
            return undefined;
        }

        const currentSentence = this.currentSentence();
        if (!currentSentence) {
            return undefined;
        }

        const currentId = currentSentence.id;
        const ids: string[] = [];

        for (const id of this.getAllIds()) {
            if (id === currentId) {
                return ids.pop();
            }
            ids.push(id);
        }

        return undefined;
    }

    nextSentence(): Sentence {
        this.paragraph = this.paragraphs[this.paragraphIndex];
        this.sentence = this.paragraph.sentences[this.sentenceIndex];

        this.sentenceIndex++;

        if (this.sentenceIndex >= this.paragraph.sentences.length) {
            this.paragraphIndex++;
            this.sentenceIndex = 0;
        }

        return this.sentence;
    }

    nextSentenceId(): string | undefined {
        return this.hasNext()
            ? this.clone().nextSentence().id : undefined;
    }

    iterateToId(sentenceId: string): void {
        this.reset();

        while (this.hasNext()) {
            this.nextSentence();

            const current = this.currentSentence();
            if (current && current.id === sentenceId) {
                return;
            }
        }
    }

    hasNext(): boolean {
        return this.paragraphIndex < this.paragraphs.length;
    }

    hasPrevious(): boolean {
        return this.previousSentenceId() !== undefined;
    }

    isEmpty(): boolean {
        return this.paragraphs.length === 0
            || this.paragraphs.every(p => p.isEmpty());
    }
}
