export default class RollingMap<T> {
    private capacity: number;
    private map: Map<string, T>;
    private keys: string[];

    constructor(capacity: number) {
        this.capacity = capacity;
        this.map = new Map();
        this.keys = [];
    }

    put(key: string, value: T): void {
        if (this.map.has(key)) {
            this.map.set(key, value);
        } else {
            if (this.keys.length === this.capacity) {
                const oldestKey = this.keys.shift();
                if (oldestKey) {
                    this.map.delete(oldestKey);
                }
            }
            this.map.set(key, value);
            this.keys.push(key);
        }
    }

    get(key: string): T | undefined {
        return this.map.get(key);
    }

    has(key: string): boolean {
        return this.map.has(key);
    }
}
