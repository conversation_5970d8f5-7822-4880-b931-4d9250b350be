
// const API_URL = 'http://localhost:8080';
const API_URL = 'http://localhost/t2s';

function startShowPageCode(apiUrl) {
    import(apiUrl + '/api.php?cmd=code&path=./sender.js')
        .then(m => m.start(apiUrl))
        .catch(e => console.log(e));
}

async function loadBgPlayerCode(apiUrl, apiKey, tabId) {

    import(apiUrl + '/api.php?cmd=code&path=./bg-player.js')
        .then(async module => {
            return module.init(apiKey);
        }).then(() => {
            chrome.runtime.sendMessage({ tabId: tabId });
        })
        .catch(e => console.log(e));
}

chrome.action.onClicked.addListener(() => {
    console.log('extension icon clicked.')
});

chrome.commands.onCommand.addListener(async command => {
    if (command === "speak_selected_text") {
        await speakSelection();
    } else if (command === "open_player_page") {
        await showPage();
    }
});

async function showPage() {
    const activeTabId = await getActiveTabId();

    if (!activeTabId) {
        return;
    }

    await chrome.scripting.executeScript({
        target : { tabId : activeTabId },
        func : startShowPageCode,
        args : [API_URL],
    });
}

async function speakSelection() {
    const activeTabId = await getActiveTabId();

    if (!activeTabId) {
        return;
    }

    const scriptLoadedToTabId = await readFromStorage('scriptLoadedToTabId');

    if (activeTabId !== scriptLoadedToTabId) {

        const apiKey = await fetchApiKey(API_URL);

        await chrome.scripting.executeScript({
            target : { tabId : activeTabId },
            func : loadBgPlayerCode,
            args : [API_URL, apiKey, activeTabId],
        });
    } else {
        await chrome.tabs.sendMessage(activeTabId, {
            command: "playOrStop"
        });
    }
}

chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
    if (message.tabId) {
        await writeToStorage({ scriptLoadedToTabId: message.tabId });

        await chrome.tabs.sendMessage(message.tabId, {
            command: "playOrStop"
        });
    }
});

async function readFromStorage(key) {
    return chrome.storage.session.get([key])
        .then(r => r[key]);
}

function writeToStorage(object) {
    return chrome.storage.session.set(object);
}

async function getActiveTabId() {
    const tabs = await chrome.tabs.query({active: true, currentWindow: true});
    return tabs[0]?.id;
}

async function fetchApiKey(url) {
    return fetch(url +'/api.php?cmd=api-key')
        .then(response => response.json())
        .then(settings => settings['api-key-google'])
        .catch(error => console.error('Failed to fetch data:', error));
}
