
// Import the 'node-fetch' package (install it with: npm install node-fetch)
import fetch from 'node-fetch';
// import fs from 'fs';

const systemInstruction = `Your role is to clean up text copied from pdf-s.

The output text will be used for text-to-speech.
Final result should be meaningful sentences but the original text may
contain computer code, parts of equations and other such things that are
not meaningful to translate to speech.

In case there are before mentioned things then these should be removed.
If some text is removed replace it with phrase "- text removed -"
The text should be split into sentences separated by "|||" (three pipe characters)

Do not output anything else except the requested info.

Example output: <sentence 1> ||| <sentence 2> ||| ...`;

const input = `But this is all getting ahead of the story. <PERSON> and his fellow mathemati-
cians did not, in fact, do any of the above sort of factoring to get the complex
roots—the finding of a single, real, positive number for the solution of a cubic
was all they were after. And, as long as mathematicians concerned themselves
with del <PERSON>’s original depressed cubic, then a single, real, positive root is
all there is, and all was well. But what of such a cubic as x3 2 6x 5 20, where
now we have p 5 26 , 0? <PERSON> would never have written such a cubic,
of course, with its negative coefficient, but rather would have written x3 5 6x
1 20 and would have considered this an entirely new problem. That is, he
would have started over from the beginning to solve`;

const api<PERSON>ey = 'AIzaSyCSnImqm2Pz3gLLwfrvtspID0bCWe09vCk';
const apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=AIzaSyBVRWpVbmuYA0d6Un30k9hDhbbmQG4KGkc';

const requestBody = {
    "system_instruction": {
        "parts":
            { "text": systemInstruction}},
    "contents": {
        "parts": {
            "text": input }
    },

    "generationConfig": {
        "temperature": 0,
        "maxOutputTokens": 4000
        // "topP": 0.8,
        // "topK": 10
    }
};

fetch(apiUrl, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestBody)
})
.then(response => response.json())
.then(data => {
    console.log('Gemini API Response:', data);
    if (data.candidates && data.candidates.length > 0) {
        const generatedText = data.candidates[0].content;
        console.log("Generated Text:", generatedText);
    } else {
        console.error("No text generated:", data);
    }

})
.catch(error => {
    console.error('Error calling Gemini API:', error);
});