import Repository from "./modules/Repository.js";

class FakeStorage {

    constructor() {
        this.store = {};
    }

    getItem(key) {
        return this.store[key];
    }

    setItem(key, value) {
        this.store[key] = value;
    }
}

const storage = new FakeStorage();
let repo = new Repository(storage);

// console.log(repo.getCurrentSlot());
// console.log(repo.getLang());
// console.log(repo.getSentenceId());

repo.saveLang('de');
repo.saveSentenceId('s1');

console.log(repo.getCurrentSlot());
console.log(repo.getLang());
console.log(repo.getSentenceId());

repo.selectSlot('1');
repo.saveLang('en');

console.log(repo.getCurrentSlot());
console.log(repo.getLang());
console.log(repo.getSentenceId());

repo.selectSlot('default');

console.log(repo.getCurrentSlot());
console.log(repo.getLang());
console.log(repo.getSentenceId());
