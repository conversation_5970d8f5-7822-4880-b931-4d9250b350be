<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Text to Speech - React</title>
    <link rel="icon" href="data:;base64,iVBORw0KGgo=">
    <link href="styles.css" rel="stylesheet">

    <script src="./libs/babel.min.js"></script>
    <script src="./libs/system.min.js"></script>
    <script type="systemjs-importmap">
        {
          "imports": {
            "react": "./libs/react.production.min.js",
            "react-dom": "./libs/react-dom.production.min.js",
            "react-router-dom": "./libs/react-router-dom.min.js"
          }
        }
    </script>

    <script>
        window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = { isDisabled: true };
    </script>

    <script type="module" src="code/t2s-systemjs-setup.js"></script>
</head>
<body>

<main id="main"></main>

</body>
</html>
