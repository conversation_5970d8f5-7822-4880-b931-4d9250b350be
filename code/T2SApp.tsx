import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import App from './components/App.tsx';

// Import TypeScript modules
import Repository from '../modules/Repository.ts';
import SectionPlayer from '../modules/SectionPlayer.ts';
import Player from '../modules/Player.ts';
import CachedAudioDownloader from '../modules/CachedAudioDownloader.ts';
import AudioDownloader from '../modules/AudioDownloader.ts';

const T2SApp: React.FC = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [repository, setRepository] = useState<Repository | null>(null);
    const [player, setPlayer] = useState<SectionPlayer | null>(null);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const initializeApp = async () => {
            try {
                // Initialize repository
                const repo = new Repository(localStorage);
                
                // Initialize player
                const apiKey = await fetchApiKey();
                const sectionPlayer = await createPlayer(apiKey);
                
                // Handle URL parameters
                const queryString = window.location.search;
                const urlParams = new URLSearchParams(queryString);
                const slotId = urlParams.get('slotId');
                const lang = urlParams.get('lang');
                
                if (slotId) {
                    repo.selectSlot(slotId);
                    repo.deleteSentenceId(repo.getCurrentSlot());
                }
                
                if (lang) {
                    repo.selectLang(lang);
                }

                // Load initial data
                const { fetchParagraphs } = await import('../../modules/dao.ts');
                const paragraphs = await fetchParagraphs(repo.getCurrentSlot());
                repo.setParagraphs(paragraphs);

                setRepository(repo);
                setPlayer(sectionPlayer);
                setIsLoading(false);
            } catch (err) {
                console.error('Failed to initialize app:', err);
                setError('Failed to initialize application');
                setIsLoading(false);
            }
        };

        initializeApp();
    }, []);

    const fetchApiKey = async (): Promise<string> => {
        const response = await fetch('settings.json');
        const settings = await response.json();
        return settings['api-key-google'];
    };

    const createPlayer = async (apiKey: string): Promise<SectionPlayer> => {
        const downloader = new AudioDownloader(apiKey);
        const cachedDownloader = new CachedAudioDownloader(downloader);
        const player = new Player(cachedDownloader);
        return new SectionPlayer(player);
    };

    if (isLoading) {
        return <div>Loading T2S Application...</div>;
    }

    if (error) {
        return <div>Error: {error}</div>;
    }

    if (!repository || !player) {
        return <div>Failed to initialize application</div>;
    }

    return <App repository={repository} player={player} />;
};

// Render the app
ReactDOM.createRoot(document.getElementById("main")!).render(
    <React.StrictMode>
        <T2SApp />
    </React.StrictMode>
);

export default T2SApp;
