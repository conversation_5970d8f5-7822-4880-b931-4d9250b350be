import React from 'react';
import cleanUpText from '../../modules/textCleanerApi.ts';

interface ClipboardManagerProps {
    onPasteFromClipboard: (text: string) => void;
    onPasteFromClipboardCleaned: (sentences: string[]) => void;
    onShowMessage: (message: string) => void;
}

const ClipboardManager: React.FC<ClipboardManagerProps> = ({
    onPasteFromClipboard,
    onPasteFromClipboardCleaned,
    onShowMessage
}) => {
    const handlePasteFromClipboard = async () => {
        try {
            onShowMessage('Reading from clipboard...');
            const text = await navigator.clipboard.readText();
            onPasteFromClipboard(text);
        } catch (error) {
            console.error('Failed to read clipboard:', error);
            onShowMessage('Failed to read clipboard');
        }
    };

    const handlePasteFromClipboardCleaned = async () => {
        try {
            const text = await navigator.clipboard.readText();
            const sentences = await cleanUpText(text);
            onPasteFromClipboardCleaned(sentences);
        } catch (error) {
            console.error('Failed to clean clipboard text:', error);
            onShowMessage('Failed to clean clipboard text');
        }
    };

    return (
        <div className="clipboard-manager">
            <button 
                id="clipboard-button"
                onClick={handlePasteFromClipboard}
            >
                From Clipboard
            </button>
            <button 
                id="clipboard-cleaned-button"
                onClick={handlePasteFromClipboardCleaned}
            >
                From Clipboard cleaned
            </button>
        </div>
    );
};

export default ClipboardManager;
