import React from 'react';

interface PlayerControlsProps {
    isPlaying: boolean;
    onPlayPause: () => void;
    onNext: () => void;
    onBack: () => void;
}

const PlayerControls: React.FC<PlayerControlsProps> = ({
    isPlaying,
    onPlayPause,
    onNext,
    onBack
}) => {
    return (
        <div className="player-controls">
            <button 
                id="play-pause-button"
                onClick={onPlayPause}
                dangerouslySetInnerHTML={{ __html: isPlaying ? 'II' : '&gt;' }}
            />
            <button 
                id="back-button"
                onClick={onBack}
                dangerouslySetInnerHTML={{ __html: '&lt;&lt;' }}
            />
            <button 
                id="next-button"
                onClick={onNext}
                dangerouslySetInnerHTML={{ __html: '&gt;&gt;' }}
            />
        </div>
    );
};

export default PlayerControls;
