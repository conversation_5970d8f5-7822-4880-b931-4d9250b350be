import React, { useEffect, useRef } from 'react';
import Paragraph from '../../modules/Paragraph.ts';

interface TextDisplayProps {
    paragraphs: Paragraph[];
    highlightedSentenceId?: string;
    onSentenceSelect: (sentenceId: string) => void;
}

const TextDisplay: React.FC<TextDisplayProps> = ({
    paragraphs,
    highlightedSentenceId,
    onSentenceSelect
}) => {
    const containerRef = useRef<HTMLDivElement>(null);

    // Auto-scroll to highlighted sentence
    useEffect(() => {
        if (highlightedSentenceId && containerRef.current) {
            const element = containerRef.current.querySelector(`#${highlightedSentenceId}`) as HTMLElement;
            if (element) {
                const rect = element.getBoundingClientRect();
                const scroll = window.scrollY;

                if (rect.top > 300) {
                    const desiredPosition = rect.top + scroll - 120;
                    window.scrollTo({ top: desiredPosition, behavior: 'smooth' });
                }
            }
        }
    }, [highlightedSentenceId]);

    const handleSentenceDoubleClick = (sentenceId: string) => {
        onSentenceSelect(sentenceId);
    };

    return (
        <div id="result-table" ref={containerRef}>
            {paragraphs.map((paragraph, paragraphIndex) => (
                <div key={`paragraph-${paragraphIndex}`} id={paragraph.id}>
                    <div>
                        {paragraph.sentences.map((sentence) => (
                            <span
                                key={sentence.id}
                                id={sentence.id}
                                style={{
                                    backgroundColor: highlightedSentenceId === sentence.id ? '#FAE5D3' : ''
                                }}
                                onDoubleClick={() => handleSentenceDoubleClick(sentence.id)}
                            >
                                {sentence.toString()}{' '}
                            </span>
                        ))}
                        <br />
                        <br />
                    </div>
                </div>
            ))}
        </div>
    );
};

export default TextDisplay;
