import React, { useEffect, useState } from 'react';
import PlayerControls from './PlayerControls.tsx';
import LanguageSelector from './LanguageSelector.tsx';
import ClipboardManager from './ClipboardManager.tsx';
import SlotManager from './SlotManager.tsx';
import TextDisplay from './TextDisplay.tsx';
import usePlayer from '../hooks/usePlayer.tsx';
import useRepository from '../hooks/useRepository.tsx';
import useTextProcessor from '../hooks/useTextProcessor.tsx';
import useKeyboardShortcuts from '../hooks/useKeyboardShortcuts.tsx';

import Repository from '../../modules/Repository.ts';
import SectionPlayer from '../../modules/SectionPlayer.ts';
import { storeParagraphs, fetchParagraphs } from '../../modules/dao.ts';

type Language = 'en' | 'no' | 'et';
type SlotId = 'default' | '1' | '2';

interface AppProps {
    repository: Repository;
    player: SectionPlayer;
}

const App: React.FC<AppProps> = ({ repository, player }) => {
    const [message, setMessage] = useState<string>('');
    
    // Custom hooks
    const playerHook = usePlayer(player);
    const repositoryHook = useRepository(repository);
    const textProcessor = useTextProcessor();

    // Keyboard shortcuts
    useKeyboardShortcuts({
        onPlayPause: playerHook.playPause,
        onNext: playerHook.next,
        onBack: playerHook.back
    });

    // Initialize player with repository data
    useEffect(() => {
        playerHook.setParagraphs(repositoryHook.paragraphs);
        playerHook.setLang(repositoryHook.currentLanguage);
        if (repositoryHook.sentenceId) {
            playerHook.goToSentence(repositoryHook.sentenceId);
        }
    }, []);

    // Handle content changes
    const handleContentChange = () => {
        playerHook.stop();
        playerHook.setParagraphs(repositoryHook.getParagraphs());
        const sentenceId = repositoryHook.getSentenceId();
        if (sentenceId) {
            playerHook.goToSentence(sentenceId);
        }
        playerHook.setLang(repositoryHook.getLang());
    };

    // Handle sentence selection
    const handleSentenceSelected = (sentenceId: string) => {
        repositoryHook.saveSentenceId(sentenceId);
        
        // Clear all highlights and highlight the selected sentence
        // This will be handled by the TextDisplay component through props
    };

    // Event handlers
    const handleLanguageChange = (language: Language) => {
        repositoryHook.selectLanguage(language);
        playerHook.setLang(language);
    };

    const handleSlotSave = async (slotId: SlotId) => {
        try {
            await storeParagraphs(repositoryHook.paragraphs, slotId);
            repositoryHook.deleteSentenceId(slotId);
            setMessage(`Saved to slot ${slotId}`);
        } catch (error) {
            console.error('Failed to save to slot:', error);
            setMessage('Failed to save');
        }
    };

    const handleSlotLoad = async (slotId: SlotId) => {
        try {
            repositoryHook.selectSlot(slotId);
            const paragraphs = await fetchParagraphs(slotId);
            repositoryHook.setParagraphs(paragraphs);
            handleContentChange();
            setMessage(`Loaded slot ${slotId}`);
        } catch (error) {
            console.error('Failed to load slot:', error);
            setMessage('Failed to load');
        }
    };

    const handlePasteFromClipboard = (text: string) => {
        const paragraphs = textProcessor.processText(text);
        repositoryHook.setParagraphs(paragraphs);
        repositoryHook.deleteSentenceId(repositoryHook.getCurrentSlot());
        handleContentChange();
    };

    const handlePasteFromClipboardCleaned = (sentences: string[]) => {
        const paragraphs = textProcessor.processCleanedSentences(sentences);
        repositoryHook.setParagraphs(paragraphs);
        repositoryHook.deleteSentenceId(repositoryHook.getCurrentSlot());
        handleContentChange();
    };

    const handleShowMessage = (msg: string) => {
        setMessage(msg);
        // Clear message after 3 seconds
        setTimeout(() => setMessage(''), 3000);
    };

    return (
        <div>
            <nav>
                <PlayerControls
                    isPlaying={playerHook.isPlaying}
                    onPlayPause={playerHook.playPause}
                    onNext={playerHook.next}
                    onBack={playerHook.back}
                />
                
                <ClipboardManager
                    onPasteFromClipboard={handlePasteFromClipboard}
                    onPasteFromClipboardCleaned={handlePasteFromClipboardCleaned}
                    onShowMessage={handleShowMessage}
                />

                <LanguageSelector
                    selectedLanguage={repositoryHook.currentLanguage}
                    onLanguageChange={handleLanguageChange}
                />

                <SlotManager
                    currentSlot={repositoryHook.currentSlot}
                    onSlotSave={handleSlotSave}
                    onSlotLoad={handleSlotLoad}
                />
            </nav>

            {message && (
                <div style={{ padding: '1rem', backgroundColor: '#f0f0f0', margin: '1rem 0' }}>
                    {message}
                </div>
            )}

            <TextDisplay
                paragraphs={repositoryHook.paragraphs}
                highlightedSentenceId={playerHook.currentSentenceId}
                onSentenceSelect={handleSentenceSelected}
            />
        </div>
    );
};

export default App;
