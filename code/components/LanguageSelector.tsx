import React from 'react';

type Language = 'en' | 'no' | 'et';

interface LanguageSelectorProps {
    selectedLanguage: Language;
    onLanguageChange: (language: Language) => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
    selectedLanguage,
    onLanguageChange
}) => {
    const languages: { value: Language; label: string }[] = [
        { value: 'en', label: 'Eng' },
        { value: 'no', label: 'No' },
        { value: 'et', label: 'Est' }
    ];

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        onLanguageChange(event.target.value as Language);
    };

    return (
        <div className="language-selector">
            {languages.map(({ value, label }) => (
                <label key={value}>
                    <input
                        type="radio"
                        name="lang"
                        value={value}
                        checked={selectedLanguage === value}
                        onChange={handleChange}
                    />
                    {label}
                </label>
            ))}
        </div>
    );
};

export default LanguageSelector;
