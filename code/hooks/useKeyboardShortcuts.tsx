import { useEffect } from 'react';

interface KeyboardShortcutsProps {
    onPlayPause: () => void;
    onNext: () => void;
    onBack: () => void;
}

const useKeyboardShortcuts = ({ onPlayPause, onNext, onBack }: KeyboardShortcutsProps) => {
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            switch (event.code) {
                case 'Space':
                    onPlayPause();
                    event.preventDefault();
                    break;
                case 'ArrowLeft':
                    onBack();
                    break;
                case 'ArrowRight':
                    onNext();
                    break;
                default:
                    break;
            }
        };

        window.addEventListener('keydown', handleKeyDown);

        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [onPlayPause, onNext, onBack]);
};

export default useKeyboardShortcuts;
