import { useState, useEffect, useCallback } from 'react';
import SectionPlayer from '../../modules/SectionPlayer.ts';
import Paragraph from '../../modules/Paragraph.ts';

type Language = 'en' | 'no' | 'et';

interface UsePlayerReturn {
    isPlaying: boolean;
    currentSentenceId: string | undefined;
    playPause: () => void;
    next: () => void;
    back: () => void;
    goToSentence: (sentenceId: string) => void;
    setParagraphs: (paragraphs: Paragraph[]) => void;
    setLang: (lang: Language) => void;
    stop: () => void;
    getAllIds: () => string[];
}

const usePlayer = (player: SectionPlayer): UsePlayerReturn => {
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentSentenceId, setCurrentSentenceId] = useState<string | undefined>();

    useEffect(() => {
        // Set up event listeners for player events
        const handleIsPlaying = () => setIsPlaying(true);
        const handleIsStopped = () => setIsPlaying(false);
        const handleSelected = (id: string) => setCurrentSentenceId(id);

        player.addEventListener('isPlaying', handleIsPlaying);
        player.addEventListener('isStopped', handleIsStopped);
        player.addEventListener('selected', handleSelected);

        // Cleanup function to remove event listeners
        return () => {
            // Note: The current SectionPlayer doesn't have removeEventListener
            // This would need to be added to the SectionPlayer class
        };
    }, [player]);

    const playPause = useCallback(() => {
        player.playPause();
    }, [player]);

    const next = useCallback(() => {
        player.next();
    }, [player]);

    const back = useCallback(() => {
        player.back();
    }, [player]);

    const goToSentence = useCallback((sentenceId: string) => {
        player.goToSentence(sentenceId);
    }, [player]);

    const setParagraphs = useCallback((paragraphs: Paragraph[]) => {
        player.setParagraphs(paragraphs);
    }, [player]);

    const setLang = useCallback((lang: Language) => {
        player.setLang(lang);
    }, [player]);

    const stop = useCallback(() => {
        player.stop();
    }, [player]);

    const getAllIds = useCallback(() => {
        return player.getAllIds();
    }, [player]);

    return {
        isPlaying,
        currentSentenceId,
        playPause,
        next,
        back,
        goToSentence,
        setParagraphs,
        setLang,
        stop,
        getAllIds
    };
};

export default usePlayer;
