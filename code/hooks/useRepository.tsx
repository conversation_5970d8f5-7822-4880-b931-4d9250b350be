import { useState, useCallback } from 'react';
import Repository from '../../modules/Repository.ts';
import Paragraph from '../../modules/Paragraph.ts';

type Language = 'en' | 'no' | 'et';
type SlotId = 'default' | '1' | '2';

interface UseRepositoryReturn {
    paragraphs: Paragraph[];
    currentSlot: SlotId;
    currentLanguage: Language;
    sentenceId: string | undefined;
    setParagraphs: (paragraphs: Paragraph[]) => void;
    selectSlot: (slotId: string) => void;
    selectLanguage: (language: string) => void;
    saveSentenceId: (sentenceId: string) => void;
    deleteSentenceId: (slotId: string) => void;
    getParagraphs: () => Paragraph[];
    getCurrentSlot: () => SlotId;
    getLang: () => Language;
    getSentenceId: () => string | undefined;
}

const useRepository = (repository: Repository): UseRepositoryReturn => {
    // Initialize state from repository
    const [paragraphs, setParagraphsState] = useState<Paragraph[]>(repository.getParagraphs());
    const [currentSlot, setCurrentSlot] = useState<SlotId>(repository.getCurrentSlot() as SlotId);
    const [currentLanguage, setCurrentLanguage] = useState<Language>(repository.getLang() as Language);
    const [sentenceId, setSentenceId] = useState<string | undefined>(repository.getSentenceId());

    const setParagraphs = useCallback((newParagraphs: Paragraph[]) => {
        repository.setParagraphs(newParagraphs);
        setParagraphsState(newParagraphs);
    }, [repository]);

    const selectSlot = useCallback((slotId: string) => {
        repository.selectSlot(slotId);
        setCurrentSlot(repository.getCurrentSlot() as SlotId);
    }, [repository]);

    const selectLanguage = useCallback((language: string) => {
        repository.selectLang(language);
        setCurrentLanguage(repository.getLang() as Language);
    }, [repository]);

    const saveSentenceId = useCallback((id: string) => {
        repository.saveSentenceId(id);
        setSentenceId(id);
    }, [repository]);

    const deleteSentenceId = useCallback((slotId: string) => {
        repository.deleteSentenceId(slotId);
        setSentenceId(repository.getSentenceId());
    }, [repository]);

    const getParagraphs = useCallback(() => {
        return repository.getParagraphs();
    }, [repository]);

    const getCurrentSlot = useCallback(() => {
        return repository.getCurrentSlot() as SlotId;
    }, [repository]);

    const getLang = useCallback(() => {
        return repository.getLang() as Language;
    }, [repository]);

    const getSentenceId = useCallback(() => {
        return repository.getSentenceId();
    }, [repository]);

    return {
        paragraphs,
        currentSlot,
        currentLanguage,
        sentenceId,
        setParagraphs,
        selectSlot,
        selectLanguage,
        saveSentenceId,
        deleteSentenceId,
        getParagraphs,
        getCurrentSlot,
        getLang,
        getSentenceId
    };
};

export default useRepository;
