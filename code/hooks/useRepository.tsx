import { useState, useCallback, useEffect } from 'react';
import Repository from '../../modules/Repository.ts';
import Paragraph from '../../modules/Paragraph.ts';

type Language = 'en' | 'no' | 'et';
type SlotId = 'default' | '1' | '2';

interface UseRepositoryReturn {
    paragraphs: Paragraph[];
    currentSlot: SlotId;
    currentLanguage: Language;
    sentenceId: string | undefined;
    isLoading: boolean;
    setParagraphs: (paragraphs: Paragraph[]) => void;
    selectSlot: (slotId: string) => Promise<void>;
    selectLanguage: (language: string) => Promise<void>;
    saveSentenceId: (sentenceId: string) => Promise<void>;
    deleteSentenceId: (slotId: string) => Promise<void>;
    getParagraphs: () => Paragraph[];
    getCurrentSlot: () => Promise<SlotId>;
    getLang: () => Promise<Language>;
    getSentenceId: () => Promise<string | undefined>;
    initializeState: () => Promise<void>;
}

const useRepository = (repository: Repository): UseRepositoryReturn => {
    // Initialize state
    const [paragraphs, setParagraphsState] = useState<Paragraph[]>(repository.getParagraphs());
    const [currentSlot, setCurrentSlot] = useState<SlotId>('default');
    const [currentLanguage, setCurrentLanguage] = useState<Language>('en');
    const [sentenceId, setSentenceId] = useState<string | undefined>(undefined);
    const [isLoading, setIsLoading] = useState<boolean>(true);

    const initializeState = useCallback(async () => {
        try {
            setIsLoading(true);
            const slot = await repository.getCurrentSlot();
            const lang = await repository.getLang();
            const sentence = await repository.getSentenceId();

            setCurrentSlot(slot as SlotId);
            setCurrentLanguage(lang as Language);
            setSentenceId(sentence);
        } catch (error) {
            console.error('Failed to initialize repository state:', error);
        } finally {
            setIsLoading(false);
        }
    }, [repository]);

    const setParagraphs = useCallback((newParagraphs: Paragraph[]) => {
        repository.setParagraphs(newParagraphs);
        setParagraphsState(newParagraphs);
    }, [repository]);

    const selectSlot = useCallback(async (slotId: string) => {
        await repository.selectSlot(slotId);
        const newSlot = await repository.getCurrentSlot();
        setCurrentSlot(newSlot as SlotId);
    }, [repository]);

    const selectLanguage = useCallback(async (language: string) => {
        await repository.selectLang(language);
        const newLang = await repository.getLang();
        setCurrentLanguage(newLang as Language);
    }, [repository]);

    const saveSentenceId = useCallback(async (id: string) => {
        await repository.saveSentenceId(id);
        setSentenceId(id);
    }, [repository]);

    const deleteSentenceId = useCallback(async (slotId: string) => {
        await repository.deleteSentenceId(slotId);
        const newSentenceId = await repository.getSentenceId();
        setSentenceId(newSentenceId);
    }, [repository]);

    const getParagraphs = useCallback(() => {
        return repository.getParagraphs();
    }, [repository]);

    const getCurrentSlot = useCallback(async () => {
        return await repository.getCurrentSlot() as SlotId;
    }, [repository]);

    const getLang = useCallback(async () => {
        return await repository.getLang() as Language;
    }, [repository]);

    const getSentenceId = useCallback(async () => {
        return await repository.getSentenceId();
    }, [repository]);

    return {
        paragraphs,
        currentSlot,
        currentLanguage,
        sentenceId,
        isLoading,
        setParagraphs,
        selectSlot,
        selectLanguage,
        saveSentenceId,
        deleteSentenceId,
        getParagraphs,
        getCurrentSlot,
        getLang,
        getSentenceId,
        initializeState
    };
};

export default useRepository;
