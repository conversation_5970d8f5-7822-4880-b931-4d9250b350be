import { useCallback } from 'react';
import TextSplitter from '../../modules/TextSplitter.ts';
import Sentence from '../../modules/Sentence.ts';
import Paragraph from '../../modules/Paragraph.ts';

interface UseTextProcessorReturn {
    processText: (text: string) => Paragraph[];
    processCleanedSentences: (sentences: string[]) => Paragraph[];
}

const useTextProcessor = (): UseTextProcessorReturn => {
    const textSplitter = new TextSplitter();

    const processText = useCallback((text: string): Paragraph[] => {
        return textSplitter.toParagraphs(text);
    }, [textSplitter]);

    const processCleanedSentences = useCallback((sentences: string[]): Paragraph[] => {
        const sentenceObjects = sentences.map(
            (sentence, index) => new Sentence(sentence, `p1_s_${index + 1}`)
        );
        return [new Paragraph(sentenceObjects)];
    }, []);

    return {
        processText,
        processCleanedSentences
    };
};

export default useTextProcessor;
