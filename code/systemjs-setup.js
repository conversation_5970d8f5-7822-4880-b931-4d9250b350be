const transform = (filename, code) => Babel.transform(code, {
    presets: ['react', 'typescript'],
    filename: filename,
    plugins: ['transform-modules-systemjs'],
}).code;

System.shouldFetch = () => true;

const needsTranslating = (filename) => {
    const endings = ['.tsx', '.ts'];
    return endings.some(ending => filename.endsWith(ending));
}

System.constructor.prototype.fetch = async url => {
    const filename = url.split('/').pop();
    if (!needsTranslating(filename)) {
        return fetch(url);
    }

    const response = await fetch(url);

    const code = transform(filename, await response.text());

    const modifiedResponse = new Response(code, {
        status: response.status,
        headers: {
            'Content-type': 'text/javascript',
        },
    });

    return Promise.resolve(modifiedResponse);
};

(async () => {
    // enforce loading order
    await System.import('react');
    await System.import('react-dom');
    await System.import('react-router-dom');
    await System.import('./code/index.tsx');
})();