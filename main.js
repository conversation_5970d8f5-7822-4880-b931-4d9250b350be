import Player from './modules/Player.js';
import Repository from "./modules/Repository.js";
import {fetchParagraphs, storeParagraphs } from "./modules/dao.js";
import SectionPlayer from "./modules/SectionPlayer.js";
import Gui from "./modules/Gui.js";
import TextSplitter from "./modules/TextSplitter.js";
import CachedAudioDownloader from "./modules/CachedAudioDownloader.js";
import AudioDownloader from "./modules/AudioDownloader.js";
import Sentence from "./modules/Sentence.js";
import Paragraph from "./modules/Paragraph.js";

const repository = new Repository(localStorage);
const player = await createPlayer(await fetchApiKey());

const queryString = window.location.search;
const urlParams = new URLSearchParams(queryString);
repository.selectSlot(urlParams.get('slotId'));
if (urlParams.get('lang')) {
    repository.selectLang(urlParams.get('lang'));
}
if (urlParams.get('slotId')) {
    repository.deleteSentenceId(repository.getCurrentSlot());
}

repository.setParagraphs(await fetchParagraphs(repository.getCurrentSlot()));

const langSelectCb = lang => {
    repository.selectLang(lang);

    player.setLang(lang);
};

const slotSaveCb = async slotId => {
    await storeParagraphs(repository.paragraphs, slotId);

    repository.deleteSentenceId(slotId);
};

const slotLoadCb = async slotId => {
    repository.selectSlot(slotId);

    const paragraphs = await fetchParagraphs(slotId);

    repository.setParagraphs(paragraphs);

    handleContentChange();
};

const selectSentenceCallback = sentenceId => {
    player.goToSentence(sentenceId);

    handleSentenceSelected(sentenceId);
};

const pasteFromClipboardCallback = text => {
    const paragraphs = new TextSplitter().toParagraphs(text);

    repository.setParagraphs(paragraphs);
    repository.deleteSentenceId(repository.getCurrentSlot());

    handleContentChange();
};

const pasteFromClipboardCleanedCallback = rawSentences => {
    const sentences = rawSentences.map(
        (sentence, index) => new Sentence(sentence, `p1_s_${index + 1}`))

    repository.setParagraphs([new Paragraph(sentences)]);
    repository.deleteSentenceId(repository.getCurrentSlot());

    handleContentChange();
};

const showMessage = message => {
    console.log(message);
};

player.addEventListener('selected', id => handleSentenceSelected(id));

const gui = new Gui(repository);

gui.addEventListener(Gui.PRESS_NEXT, () => player.next());
gui.addEventListener(Gui.PRESS_BACK, () => player.back());
gui.addEventListener(Gui.PRESS_PLAY_PAUSE, () => player.playPause());
gui.addEventListener(Gui.LANG_SELECT, lang => langSelectCb(lang));
gui.addEventListener(Gui.SLOT_SAVE, slotId => slotSaveCb(slotId));
gui.addEventListener(Gui.SLOT_LOAD, slotId => slotLoadCb(slotId));
gui.addEventListener(Gui.SELECT_SENTENCE, slotId => selectSentenceCallback(slotId));
gui.addEventListener(Gui.PASTE_FROM_CLIPBOARD, slotId => pasteFromClipboardCallback(slotId));
gui.addEventListener(Gui.PASTE_FROM_CLIPBOARD_CLEANED, slotId => pasteFromClipboardCleanedCallback(slotId));
gui.addEventListener(Gui.SHOW_MESSAGE, message => showMessage(message));

window.addEventListener('keydown', (e) => {
    if (e.code === "Space") {
        player.playPause();
        e.preventDefault();
    } else if (e.code === "ArrowLeft") {
        player.back();
    } else if (e.code === "ArrowRight") {
        player.next();
    }
});

player.addEventListener('isPlaying', () => {
    gui.setIsPlaying(true);
});

player.addEventListener('isStopped', () => {
    gui.setIsPlaying(false);
});

player.addEventListener('selected', id => {
    handleSentenceSelected(id);
});

handleContentChange();

function handleContentChange() {
    gui.refresh();

    player.stop();

    player.setParagraphs(repository.getParagraphs());
    player.goToSentence(repository.getSentenceId());
    player.setLang(repository.getLang());

    handleSentenceSelected(player.currentSentenceId());
}

function handleSentenceSelected(sentenceId) {
    repository.saveSentenceId(sentenceId);

    player.getAllIds().forEach(id => gui.clearHighlight(id));

    gui.highlightSentence(sentenceId);

    gui.focusSentence(sentenceId);
}

async function createPlayer(apiKey) {
    const downloader = new AudioDownloader(apiKey);
    const cachedDownloader = new CachedAudioDownloader(downloader);

    return new SectionPlayer(new Player(cachedDownloader));
}

function fetchApiKey() {
    return fetch('settings.json')
        .then(response => response.json())
        .then(settings => settings['api-key-google']);
}
