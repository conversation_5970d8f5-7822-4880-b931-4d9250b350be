import TextSplitter from "../modules/TextSplitter.js";

describe("TextSplitter", () => {

    it("splits text into paragraphs and sentences", () => {
        const text = "s1. s2 \n s3? \r\n \n s4"

        const paragraphs = new TextSplitter().toParagraphs(text);

        expect(paragraphs[0].size()).toBe(3);
        expect(paragraphs[1].size()).toBe(1);
        expect(paragraphs[1].sentences[0].id).toBe('p_2_s_1');
        expect(paragraphs[1].sentences[0].text).toEqual('s4');
    });


});

