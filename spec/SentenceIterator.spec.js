import SentenceIterator from "../modules/SentenceIterator.js";
import TextSplitter from "../modules/TextSplitter.js";

describe("SentenceIterator", () => {

    it("can get all id-s", () => {
        const it = new SentenceIterator();
        const paragraphs = new TextSplitter().toParagraphs("s1. s2 \n\n s3")
        it.load(paragraphs);

        expect(it.getAllIds()).toEqual([ 'p_1_s_1', 'p_1_s_2', 'p_2_s_1' ]);
    });

    it("containsSentenceId()", () => {
        const it = new SentenceIterator();
        const paragraphs = new TextSplitter().toParagraphs("s1. s2 \n\n s3")
        it.load(paragraphs);

        expect(it.hasNext()).toBe(true);
        expect(it.containsSentenceId('p_2_s_1')).toBe(true);
    });

    it("can go to previous sentence", () => {
        const it = new SentenceIterator();
        const paragraphs = new TextSplitter().toParagraphs("s1. s2 \n\n s3")
        it.load(paragraphs);

        expect(it.previousSentenceId()).toBe(undefined);
        it.nextSentence();
        expect(it.previousSentenceId()).toBe(undefined);
        it.nextSentence();
        expect(it.previousSentenceId()).toBe('p_1_s_1');
        expect(it.currentSentence().id).toBe('p_1_s_2');
    });

    it("can get next sentence id", () => {
        const it = new SentenceIterator();
        const paragraphs = new TextSplitter().toParagraphs("s1. s2 \n\n s3")
        it.load(paragraphs);

        it.nextSentence();

        expect(it.currentSentence().id).toBe('p_1_s_1');
        expect(it.nextSentenceId()).toBe('p_1_s_2');
    });

    it("can iterate to id", () => {
        const it = new SentenceIterator();
        const paragraphs = new TextSplitter().toParagraphs("s1. s2 \n\n s3")
        it.load(paragraphs);

        it.iterateToId('p_1_s_2');

        expect(it.currentSentence().id).toBe('p_1_s_2');
    });

});
