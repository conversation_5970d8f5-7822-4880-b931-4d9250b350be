import { describe, it, expect } from '@jest/globals';
import SectionPlayer from "../modules/SectionPlayer.js";
import {FakePlayer} from "./FakePlayer.js";
import TextSplitter from "../modules/TextSplitter.js";

describe("SectionPlayer", function() {

    let events: any[] = [];
    const player = new SectionPlayer(new FakePlayer() as any);
    player.addEventListener('selected', (id: any) => events.push(['selected', id]));

    it("Nothing is selected or triggered when paragraphs is empty", function() {
        events = [];

        player.setParagraphs([]);
        player.goToSentence('random-id');
        expect(events.length).toBe(0);
        expect(player.currentSentenceId()).toBe(undefined);
    });

    it("Select first sentence when id not found", function() {
        events = [];

        const paragraphs = new TextSplitter().toParagraphs("s1. s2 \n\n s3")
        player.setParagraphs(paragraphs);
        player.goToSentence('random-id');

        expect(events.length).toBe(1);
        expect(events).toEqual([[ 'selected', 'p_1_s_1' ]]);
    });

    it("Select the sentence when the id is found", function() {
        events = [];

        const paragraphs = new TextSplitter().toParagraphs("s1. s2 \n\n s3")
        player.setParagraphs(paragraphs);
        player.goToSentence('p_2_s_1');

        expect(events.length).toBe(1);
        expect(events).toEqual([[ 'selected', 'p_2_s_1' ]]);
        expect(player.currentSentenceId()).toEqual('p_2_s_1');
    });
});
