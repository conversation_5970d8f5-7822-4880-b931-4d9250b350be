
type Language = 'en' | 'no' | 'et';
type PlayerEvent = 'ended' | 'isPlaying' | 'isStopped';

export class FakePlayer {

    play(textToPlay: string, lang: Language): Promise<void> {
        console.log('play');
        return Promise.resolve();
    }

    pause(): void {
        console.log('pause');
    }

    stop(): void {
        console.log('stop');
    }

    isPlaying(): boolean {
        return false;
    }

    isSuspended(): boolean {
        return false;
    }

    resume(): void {
        console.log('resume');
    }

    addEventListener(event: PlayerEvent, listener: () => void): void {
        // Mock implementation
    }

}