<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>

<audio></audio>

<script type="module">
    let audioContext = new AudioContext();

    const files = ['audio1.mp3', 'audio2.mp3'];
    let currentFile = 0;

    document.getElementById('next-button').addEventListener('click', play);

    async function fetchFile(fileName) {
        const audioBuffer = await fetch(fileName)
            .then(response => response.arrayBuffer())
            .then(arrayBuffer => audioContext.decodeAudioData(arrayBuffer))
            .catch(error => console.error(error));
        return audioBuffer;
    }

    function fetchAudio(text) {
        const url = 'https://api.tartunlp.ai/text-to-speech/v2';

        return fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text: text,
                "speaker": "mari",
                "speed": 1
            })
        })
            .then(response => response.arrayBuffer())
            .then(arrayBuffer => audioContext.decodeAudioData(arrayBuffer))
            .catch(error => console.error(error));
    }

    async function play() {
        const audioBuffer = await fetchAudio('Tere, maailm!');

        audioContext = new AudioContext();

        const source = audioContext.createBufferSource();
        source.buffer = audioBuffer;

        source.connect(audioContext.destination);
        source.start();
    }

</script>

<main>
player
</main>

<br>

<button id="next-button">Next</button>

<br><br>

</body>
</html>